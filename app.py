"""
节点销售管理系统 - Flask应用主文件
"""
import os
import logging
from datetime import datetime, timezone
from flask import Flask, jsonify, request, g, render_template, redirect, url_for
from flask_cors import CORS
from werkzeug.exceptions import HTTPException

# 导入配置
from config import Config, config

# 导入数据库模型
from models import db
from flask_migrate import Migrate

# 导入路由蓝图
from routes.auth import auth_bp
from routes.admin import admin_bp
from routes.shop import shop_bp
from routes.user import user_bp
from routes.subscription import subscription_bp
from routes.api import api_bp
from routes.verification import verification_bp
from routes.renewal import renewal_bp

def create_app(config_name=None):
    """
    Flask应用工厂函数
    
    Args:
        config_name: 配置名称 ('development', 'production', 'default')
    
    Returns:
        Flask应用实例
    """
    # 创建Flask应用
    app = Flask(__name__)
    
    # 加载配置
    if config_name is None:
        config_name = os.environ.get('FLASK_ENV', 'default')
    
    app.config.from_object(config[config_name])
    
    # 初始化扩展
    init_extensions(app)
    
    # 注册蓝图
    register_blueprints(app)
    
    # 注册错误处理器
    register_error_handlers(app)
    
    # 配置日志
    configure_logging(app)
    
    # 注册请求处理器
    register_request_handlers(app)
    
    # 注册系统路由
    register_system_routes(app)
    
    return app

def init_extensions(app):
    """初始化Flask扩展"""
    # 初始化数据库
    db.init_app(app)

    # 初始化Flask-Migrate
    migrate = Migrate(app, db)

    # 初始化CORS
    CORS(app, resources={
        r"/api/*": {
            "origins": "*",
            "methods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
            "allow_headers": ["Content-Type", "Authorization"]
        }
    })

    # 初始化配置服务（在应用上下文中）
    with app.app_context():
        try:
            from services.config_service import config_service
            # 预加载配置，确保配置服务正常工作
            panels = config_service.get_xui_panels()
            app.logger.info(f"配置服务初始化完成，加载了 {len(panels)} 个面板配置")
        except Exception as e:
            app.logger.warning(f"配置服务初始化失败: {e}")

    # 初始化定时任务服务
    try:
        from services.scheduler_service import scheduler_service
        scheduler_service.init_app(app)
        scheduler_service.start()
        app.logger.info("定时任务服务初始化完成")
    except Exception as e:
        app.logger.warning(f"定时任务服务初始化失败: {e}")

def register_blueprints(app):
    """注册蓝图"""
    # 认证路由
    app.register_blueprint(auth_bp, url_prefix='/auth')

    # 管理员路由
    app.register_blueprint(admin_bp, url_prefix='/admin')

    # 商店路由
    app.register_blueprint(shop_bp, url_prefix='/shop')

    # 用户中心路由
    app.register_blueprint(user_bp, url_prefix='/user')

    # 订阅路由（无前缀，直接在根路径下）
    app.register_blueprint(subscription_bp)

    # API路由
    app.register_blueprint(api_bp, url_prefix='/api')

    # 验证码路由
    app.register_blueprint(verification_bp)

    # 续费路由
    app.register_blueprint(renewal_bp, url_prefix='/renewal')

def register_error_handlers(app):
    """注册错误处理器"""
    
    @app.errorhandler(404)
    def not_found(error):
        """404错误处理"""
        if request.path.startswith('/api/'):
            return jsonify({
                'error': 'Not Found',
                'message': '请求的资源不存在',
                'status_code': 404
            }), 404
        return jsonify({
            'error': 'Page Not Found',
            'message': '页面不存在',
            'status_code': 404
        }), 404
    
    @app.errorhandler(500)
    def internal_error(error):
        """500错误处理"""
        db.session.rollback()
        app.logger.error(f'服务器内部错误: {str(error)}')
        return jsonify({
            'error': 'Internal Server Error',
            'message': '服务器内部错误，请稍后重试',
            'status_code': 500
        }), 500
    
    @app.errorhandler(400)
    def bad_request(error):
        """400错误处理"""
        return jsonify({
            'error': 'Bad Request',
            'message': '请求参数错误',
            'status_code': 400
        }), 400
    
    @app.errorhandler(401)
    def unauthorized(error):
        """401错误处理"""
        return jsonify({
            'error': 'Unauthorized',
            'message': '未授权访问',
            'status_code': 401
        }), 401
    
    @app.errorhandler(403)
    def forbidden(error):
        """403错误处理"""
        return jsonify({
            'error': 'Forbidden',
            'message': '禁止访问',
            'status_code': 403
        }), 403
    
    @app.errorhandler(HTTPException)
    def handle_http_exception(error):
        """通用HTTP异常处理"""
        return jsonify({
            'error': error.name,
            'message': error.description,
            'status_code': error.code
        }), error.code

def configure_logging(app):
    """配置日志"""
    if not app.debug and not app.testing:
        # 生产环境日志配置
        if not os.path.exists('logs'):
            os.mkdir('logs')
        
        file_handler = logging.FileHandler('logs/node_sales.log')
        file_handler.setFormatter(logging.Formatter(
            '%(asctime)s %(levelname)s: %(message)s [in %(pathname)s:%(lineno)d]'
        ))
        file_handler.setLevel(logging.INFO)
        app.logger.addHandler(file_handler)
        
        app.logger.setLevel(logging.INFO)
        app.logger.info('节点销售系统启动')

def register_request_handlers(app):
    """注册请求处理器"""
    
    @app.before_request
    def before_request():
        """请求前处理"""
        g.start_time = datetime.now(timezone.utc)

        # 记录API请求
        if request.path.startswith('/api/'):
            app.logger.info(f'API请求: {request.method} {request.path} - IP: {request.remote_addr}')

    @app.after_request
    def after_request(response):
        """请求后处理"""
        # 计算请求处理时间
        if hasattr(g, 'start_time'):
            duration = (datetime.now(timezone.utc) - g.start_time).total_seconds()
            response.headers['X-Response-Time'] = f'{duration:.3f}s'
        
        # 添加安全头
        response.headers['X-Content-Type-Options'] = 'nosniff'
        response.headers['X-Frame-Options'] = 'DENY'
        response.headers['X-XSS-Protection'] = '1; mode=block'
        
        return response

def register_system_routes(app):
    """注册系统路由"""
    
    @app.route('/')
    def index():
        """首页 - 重定向到商店"""
        return redirect(url_for('shop.shop_index'))

    @app.route('/health')
    def health_check():
        """健康检查"""
        try:
            # 检查数据库连接
            db.session.execute('SELECT 1')
            db_status = 'healthy'
        except Exception as e:
            db_status = f'error: {str(e)}'

        return jsonify({
            'status': 'healthy' if db_status == 'healthy' else 'unhealthy',
            'timestamp': datetime.now(timezone.utc).isoformat(),
            'services': {
                'database': db_status,
                'application': 'healthy'
            },
            'version': '1.0.0'
        })

    @app.route('/api/system/info')
    def system_info():
        """系统信息"""
        return jsonify({
            'system': '节点销售管理系统',
            'version': '1.0.0',
            'environment': app.config.get('ENV', 'development'),
            'debug': app.debug,
            'database_uri': app.config['SQLALCHEMY_DATABASE_URI'].split('@')[-1] if '@' in app.config['SQLALCHEMY_DATABASE_URI'] else 'sqlite',
            'timestamp': datetime.now(timezone.utc).isoformat()
        })

if __name__ == '__main__':
    # 直接运行时的配置
    app = create_app()
    app.run(host='0.0.0.0', port=5000, debug=True)
