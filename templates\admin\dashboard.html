{% extends "base.html" %}

{% block title %}管理后台 - 节点商城{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="row">
        <div class="col-12">
            <h1 class="mb-4">
                <i class="bi bi-speedometer2"></i> 管理后台
            </h1>
        </div>
    </div>
    
    <!-- 统计卡片 -->
    <div class="row mb-5">
        <div class="col-lg-3 col-md-6 mb-4 fade-in" style="animation-delay: 0.1s;">
            <div class="card border-0 shadow-sm stats-card-primary text-white h-100 hover-lift">
                <div class="card-body p-4">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2 class="fw-bold mb-1">{{ stats.total_users }}</h2>
                            <p class="mb-0 opacity-75">总用户数</p>
                            <small class="opacity-50">
                                <i class="bi bi-arrow-up me-1"></i>
                                较上月增长 12%
                            </small>
                        </div>
                        <div class="bg-white bg-opacity-25 rounded-circle d-flex align-items-center justify-content-center pulse" style="width: 60px; height: 60px;">
                            <i class="bi bi-people" style="font-size: 1.5rem;"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-4 fade-in" style="animation-delay: 0.2s;">
            <div class="card border-0 shadow-sm stats-card-success text-white h-100 hover-lift">
                <div class="card-body p-4">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2 class="fw-bold mb-1">{{ stats.total_orders }}</h2>
                            <p class="mb-0 opacity-75">总订单数</p>
                            <small class="opacity-50">
                                <i class="bi bi-arrow-up me-1"></i>
                                今日新增 {{ stats.today_orders }} 个
                            </small>
                        </div>
                        <div class="bg-white bg-opacity-25 rounded-circle d-flex align-items-center justify-content-center" style="width: 60px; height: 60px;">
                            <i class="bi bi-cart-check" style="font-size: 1.5rem;"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-4 fade-in" style="animation-delay: 0.3s;">
            <div class="card border-0 shadow-sm stats-card-info text-white h-100 hover-lift">
                <div class="card-body p-4">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2 class="fw-bold mb-1">{{ stats.online_panels }}/{{ stats.total_panels }}</h2>
                            <p class="mb-0 opacity-75">在线面板</p>
                            <small class="opacity-50">
                                <i class="bi bi-check-circle me-1"></i>
                                运行状态良好
                            </small>
                        </div>
                        <div class="bg-white bg-opacity-25 rounded-circle d-flex align-items-center justify-content-center" style="width: 60px; height: 60px;">
                            <i class="bi bi-server" style="font-size: 1.5rem;"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-4 fade-in" style="animation-delay: 0.4s;">
            <div class="card border-0 shadow-sm stats-card-warning text-white h-100 hover-lift">
                <div class="card-body p-4">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2 class="fw-bold mb-1">¥{{ "%.0f"|format(stats.total_revenue) }}</h2>
                            <p class="mb-0 opacity-75">总收入</p>
                            <small class="opacity-50">
                                <i class="bi bi-graph-up me-1"></i>
                                本月收入良好
                            </small>
                        </div>
                        <div class="bg-white bg-opacity-25 rounded-circle d-flex align-items-center justify-content-center" style="width: 60px; height: 60px;">
                            <i class="bi bi-currency-yen" style="font-size: 1.5rem;"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 快捷操作 -->
    <div class="row mb-4">
        <div class="col-12">
            <h3 class="fw-bold mb-3">快捷操作</h3>
            <p class="text-muted">常用管理功能快速入口</p>
        </div>
    </div>

    <div class="row g-4">
        <div class="col-lg-3 col-md-6 scale-in" style="animation-delay: 0.1s;">
            <div class="card border-0 shadow-sm h-100 text-center hover-lift">
                <div class="card-body p-4">
                    <div class="bg-primary bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center mb-3 hover-scale" style="width: 80px; height: 80px;">
                        <i class="bi bi-people text-primary" style="font-size: 2rem;"></i>
                    </div>
                    <h5 class="fw-bold mb-2">用户管理</h5>
                    <p class="text-muted mb-4">管理系统用户账户和权限</p>
                    <a href="{{ url_for('admin.users') }}" class="btn btn-primary btn-animated">
                        <i class="bi bi-arrow-right me-1"></i> 进入管理
                    </a>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 scale-in" style="animation-delay: 0.2s;">
            <div class="card border-0 shadow-sm h-100 text-center hover-lift">
                <div class="card-body p-4">
                    <div class="bg-success bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center mb-3 hover-scale" style="width: 80px; height: 80px;">
                        <i class="bi bi-server text-success" style="font-size: 2rem;"></i>
                    </div>
                    <h5 class="fw-bold mb-2">面板管理</h5>
                    <p class="text-muted mb-4">管理X-UI面板和节点配置</p>
                    <a href="{{ url_for('admin.panels') }}" class="btn btn-success btn-animated">
                        <i class="bi bi-arrow-right me-1"></i> 进入管理
                    </a>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 scale-in" style="animation-delay: 0.3s;">
            <div class="card border-0 shadow-sm h-100 text-center hover-lift">
                <div class="card-body p-4">
                    <div class="bg-info bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center mb-3 hover-scale" style="width: 80px; height: 80px;">
                        <i class="bi bi-collection text-info" style="font-size: 2rem;"></i>
                    </div>
                    <h5 class="fw-bold mb-2">分组管理</h5>
                    <p class="text-muted mb-4">管理面板分组和组织结构</p>
                    <a href="{{ url_for('admin.groups') }}" class="btn btn-info btn-animated">
                        <i class="bi bi-arrow-right me-1"></i> 进入管理
                    </a>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 scale-in" style="animation-delay: 0.4s;">
            <div class="card border-0 shadow-sm h-100 text-center hover-lift">
                <div class="card-body p-4">
                    <div class="bg-warning bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center mb-3 hover-scale" style="width: 80px; height: 80px;">
                        <i class="bi bi-box text-warning" style="font-size: 2rem;"></i>
                    </div>
                    <h5 class="fw-bold mb-2">产品管理</h5>
                    <p class="text-muted mb-4">管理销售产品和套餐配置</p>
                    <a href="{{ url_for('admin.products') }}" class="btn btn-warning btn-animated">
                        <i class="bi bi-arrow-right me-1"></i> 进入管理
                    </a>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 scale-in" style="animation-delay: 0.5s;">
            <div class="card border-0 shadow-sm h-100 text-center hover-lift">
                <div class="card-body p-4">
                    <div class="bg-danger bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center mb-3 hover-scale" style="width: 80px; height: 80px;">
                        <i class="bi bi-list-ul text-danger" style="font-size: 2rem;"></i>
                    </div>
                    <h5 class="fw-bold mb-2">订单管理</h5>
                    <p class="text-muted mb-4">查看和管理用户订单</p>
                    <a href="{{ url_for('admin.orders') }}" class="btn btn-danger btn-animated">
                        <i class="bi bi-arrow-right me-1"></i> 进入管理
                    </a>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 scale-in" style="animation-delay: 0.6s;">
            <div class="card border-0 shadow-sm h-100 text-center hover-lift">
                <div class="card-body p-4">
                    <div class="bg-purple bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center mb-3 hover-scale" style="width: 80px; height: 80px;">
                        <i class="bi bi-link-45deg text-purple" style="font-size: 2rem;"></i>
                    </div>
                    <h5 class="fw-bold mb-2">订阅管理</h5>
                    <p class="text-muted mb-4">管理用户订阅和流量统计</p>
                    <a href="{{ url_for('admin.subscriptions') }}" class="btn btn-purple btn-animated">
                        <i class="bi bi-arrow-right me-1"></i> 进入管理
                    </a>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 scale-in" style="animation-delay: 0.7s;">
            <div class="card border-0 shadow-sm h-100 text-center hover-lift">
                <div class="card-body p-4">
                    <div class="bg-warning bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center mb-3 hover-scale" style="width: 80px; height: 80px;">
                        <i class="bi bi-arrow-clockwise text-warning" style="font-size: 2rem;"></i>
                    </div>
                    <h5 class="fw-bold mb-2">续费配置</h5>
                    <p class="text-muted mb-4">设置续费折扣和价格策略</p>
                    <a href="{{ url_for('admin.renewal_config') }}" class="btn btn-warning btn-animated text-dark fw-bold">
                        <i class="bi bi-arrow-right me-1"></i> 进入管理
                    </a>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 scale-in" style="animation-delay: 0.7s;">
            <div class="card border-0 shadow-sm h-100 text-center hover-lift">
                <div class="card-body p-4">
                    <div class="bg-secondary bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center mb-3 hover-scale" style="width: 80px; height: 80px;">
                        <i class="bi bi-envelope-gear text-secondary" style="font-size: 2rem;"></i>
                    </div>
                    <h5 class="fw-bold mb-2">邮件配置</h5>
                    <p class="text-muted mb-4">管理邮件服务器和通知设置</p>
                    <a href="{{ url_for('admin.email_config') }}" class="btn btn-secondary btn-animated">
                        <i class="bi bi-arrow-right me-1"></i> 进入管理
                    </a>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 scale-in" style="animation-delay: 0.8s;">
            <div class="card border-0 shadow-sm h-100 text-center hover-lift">
                <div class="card-body p-4">
                    <div class="bg-teal bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center mb-3 hover-scale" style="width: 80px; height: 80px;">
                        <i class="bi bi-ticket-percent text-teal" style="font-size: 2rem;"></i>
                    </div>
                    <h5 class="fw-bold mb-2">优惠券管理</h5>
                    <p class="text-muted mb-4">创建和管理优惠券</p>
                    <a href="{{ url_for('admin.list_coupons') }}" class="btn btn-teal btn-animated">
                        <i class="bi bi-arrow-right me-1"></i> 进入管理
                    </a>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 scale-in" style="animation-delay: 0.9s;">
            <div class="card border-0 shadow-sm h-100 text-center hover-lift">
                <div class="card-body p-4">
                    <div class="bg-primary bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center mb-3 hover-scale" style="width: 80px; height: 80px;">
                        <i class="bi bi-file-code text-primary" style="font-size: 2rem;"></i>
                    </div>
                    <h5 class="fw-bold mb-2">协议模板</h5>
                    <p class="text-muted mb-4">管理多协议配置模板</p>
                    <a href="{{ url_for('protocol_template.list_templates') }}" class="btn btn-primary btn-animated">
                        <i class="bi bi-arrow-right me-1"></i> 进入管理
                    </a>
                </div>
            </div>
        </div>

    </div>
    
    <!-- 今日数据 -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-calendar-day"></i> 今日数据
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>今日订单：</strong> {{ stats.today_orders }} 个</p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>活跃产品：</strong> {{ stats.total_products }} 个</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
