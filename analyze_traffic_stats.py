#!/usr/bin/env python3
"""
分析流量统计数据
"""
from app import create_app
from models import db, TrafficStats, Subscription, Order
from sqlalchemy import text, func
from datetime import datetime, timedelta
import sqlite3

def analyze_traffic_stats():
    """分析流量统计数据"""
    app = create_app()
    with app.app_context():
        print('=== 流量统计数据分析 ===\n')
        
        # 1. 基本统计
        print('1. 基本统计:')
        
        total_stats = TrafficStats.query.count()
        print(f'   总流量统计记录: {total_stats} 条')
        
        # 获取活跃订阅
        active_subscriptions = Subscription.query.filter_by(is_active=True).all()
        active_sub_ids = [sub.id for sub in active_subscriptions]
        print(f'   活跃订阅数量: {len(active_subscriptions)}')
        print(f'   活跃订阅ID: {active_sub_ids}')
        
        # 2. 按订阅分组统计
        print(f'\n2. 按订阅分组统计:')
        
        # 查询每个订阅的流量统计数量
        subscription_stats = db.session.query(
            TrafficStats.subscription_id,
            func.count(TrafficStats.id).label('count'),
            func.min(TrafficStats.recorded_at).label('earliest'),
            func.max(TrafficStats.recorded_at).label('latest')
        ).group_by(TrafficStats.subscription_id).all()
        
        print(f'   有流量统计的订阅数量: {len(subscription_stats)}')
        
        for stat in subscription_stats:
            sub_id, count, earliest, latest = stat
            
            # 检查订阅是否存在
            subscription = Subscription.query.get(sub_id)
            if subscription:
                status = "活跃" if subscription.is_active else "非活跃"
                print(f'   订阅 {sub_id}: {count} 条记录 ({status})')
                print(f'     时间范围: {earliest} 到 {latest}')
            else:
                print(f'   订阅 {sub_id}: {count} 条记录 (订阅不存在 - 孤儿数据)')
        
        # 3. 检查孤儿流量统计
        print(f'\n3. 检查孤儿流量统计:')
        
        if active_sub_ids:
            orphan_stats = TrafficStats.query.filter(
                ~TrafficStats.subscription_id.in_(active_sub_ids)
            ).all()
        else:
            orphan_stats = TrafficStats.query.all()
        
        print(f'   孤儿流量统计: {len(orphan_stats)} 条')
        
        if orphan_stats:
            # 按订阅ID分组孤儿数据
            orphan_by_sub = {}
            for stat in orphan_stats:
                if stat.subscription_id not in orphan_by_sub:
                    orphan_by_sub[stat.subscription_id] = []
                orphan_by_sub[stat.subscription_id].append(stat)
            
            print(f'   孤儿数据涉及的订阅ID: {list(orphan_by_sub.keys())}')
            
            for sub_id, stats in orphan_by_sub.items():
                print(f'     订阅 {sub_id}: {len(stats)} 条孤儿记录')
                
                # 检查这个订阅是否曾经存在
                subscription = Subscription.query.get(sub_id)
                if subscription:
                    print(f'       订阅状态: {"活跃" if subscription.is_active else "非活跃"}')
                else:
                    print(f'       订阅状态: 不存在（已删除）')
        
        # 4. 时间分布分析
        print(f'\n4. 时间分布分析:')
        
        # 获取最早和最晚的记录
        earliest_stat = TrafficStats.query.order_by(TrafficStats.recorded_at.asc()).first()
        latest_stat = TrafficStats.query.order_by(TrafficStats.recorded_at.desc()).first()

        if earliest_stat and latest_stat:
            print(f'   最早记录: {earliest_stat.recorded_at}')
            print(f'   最晚记录: {latest_stat.recorded_at}')

            # 计算时间跨度
            if earliest_stat.recorded_at and latest_stat.recorded_at:
                time_span = latest_stat.recorded_at - earliest_stat.recorded_at
                print(f'   时间跨度: {time_span.days} 天')
        
        # 按日期统计记录数量
        try:
            # 使用原生SQL查询按日期分组
            result = db.session.execute(text("""
                SELECT DATE(timestamp) as date, COUNT(*) as count 
                FROM traffic_stats 
                GROUP BY DATE(timestamp) 
                ORDER BY date DESC 
                LIMIT 10
            """))
            
            daily_stats = list(result)
            print(f'   最近10天的记录分布:')
            for date, count in daily_stats:
                print(f'     {date}: {count} 条记录')
                
        except Exception as e:
            print(f'   按日期统计失败: {e}')
        
        # 5. 流量数据分析
        print(f'\n5. 流量数据分析:')
        
        # 计算总流量
        total_upload = db.session.query(func.sum(TrafficStats.upload_bytes)).scalar() or 0
        total_download = db.session.query(func.sum(TrafficStats.download_bytes)).scalar() or 0
        total_traffic = total_upload + total_download
        
        print(f'   总上传流量: {total_upload / (1024**3):.2f} GB')
        print(f'   总下载流量: {total_download / (1024**3):.2f} GB')
        print(f'   总流量: {total_traffic / (1024**3):.2f} GB')
        
        # 按订阅统计流量
        subscription_traffic = db.session.query(
            TrafficStats.subscription_id,
            func.sum(TrafficStats.upload_bytes + TrafficStats.download_bytes).label('total_bytes')
        ).group_by(TrafficStats.subscription_id).all()
        
        print(f'   各订阅流量统计:')
        for sub_id, total_bytes in subscription_traffic:
            total_gb = (total_bytes or 0) / (1024**3)
            subscription = Subscription.query.get(sub_id)
            status = "存在" if subscription else "不存在"
            print(f'     订阅 {sub_id}: {total_gb:.2f} GB ({status})')
        
        # 6. 流量统计生成机制分析
        print(f'\n6. 流量统计生成机制分析:')
        
        print(f'   流量统计的生成原因:')
        print(f'   1. 定时任务收集: 系统每5分钟收集一次流量数据')
        print(f'   2. 历史累积: 随着时间推移，记录会不断增加')
        print(f'   3. 多订阅历史: 如果有多个订阅（包括已删除的），会有更多记录')
        print(f'   4. 测试数据: 开发和测试过程中产生的数据')
        
        # 检查是否有重复记录
        duplicate_check = db.session.execute(text("""
            SELECT subscription_id, timestamp, COUNT(*) as count
            FROM traffic_stats 
            GROUP BY subscription_id, timestamp 
            HAVING COUNT(*) > 1
            LIMIT 5
        """))
        
        duplicates = list(duplicate_check)
        if duplicates:
            print(f'   发现重复记录: {len(duplicates)} 组')
            for sub_id, timestamp, count in duplicates:
                print(f'     订阅 {sub_id} 在 {timestamp}: {count} 条重复记录')
        else:
            print(f'   ✅ 没有发现重复记录')
        
        # 7. 数据清理建议
        print(f'\n7. 数据清理建议:')
        
        # 计算可清理的数据
        cleanable_count = 0
        
        # 孤儿数据（引用不存在订阅的记录）
        orphan_count = len(orphan_stats)
        if orphan_count > 0:
            cleanable_count += orphan_count
            print(f'   可清理孤儿数据: {orphan_count} 条')
        
        # 非活跃订阅的旧数据（可选清理）
        inactive_stats = TrafficStats.query.join(Subscription).filter(
            Subscription.is_active == False
        ).count()
        
        if inactive_stats > 0:
            print(f'   非活跃订阅数据: {inactive_stats} 条 (可选清理)')
        
        # 旧数据（超过30天的数据，可选清理）
        thirty_days_ago = datetime.now() - timedelta(days=30)
        old_stats = TrafficStats.query.filter(
            TrafficStats.timestamp < thirty_days_ago
        ).count()
        
        if old_stats > 0:
            print(f'   30天前的数据: {old_stats} 条 (可选清理)')
        
        # 8. 总结
        print(f'\n8. 总结:')
        
        print(f'   📊 流量统计数据分析结果:')
        print(f'   - 总记录数: {total_stats} 条')
        print(f'   - 孤儿数据: {orphan_count} 条')
        print(f'   - 活跃订阅数据: {total_stats - orphan_count} 条')
        
        if orphan_count == 0:
            print(f'   ✅ 没有孤儿数据，所有流量统计都有有效的订阅关联')
        elif orphan_count < total_stats * 0.1:  # 少于10%
            print(f'   🟡 有少量孤儿数据，建议清理')
        else:
            print(f'   🔴 有较多孤儿数据，建议立即清理')
        
        print(f'\n   💡 200多条流量统计的原因:')
        print(f'   - 这是正常现象，系统每5分钟收集一次流量数据')
        print(f'   - 如果系统运行了几天/几周，会累积大量记录')
        print(f'   - 每个订阅每天会产生 288 条记录 (24小时 × 12次/小时)')
        print(f'   - 多个订阅和历史数据会快速累积')
        
        print(f'\n=== 流量统计数据分析完成 ===')

if __name__ == '__main__':
    analyze_traffic_stats()
