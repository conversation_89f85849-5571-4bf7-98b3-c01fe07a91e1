{% extends "base.html" %}

{% block title %}订阅管理{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">订阅管理</h3>
                    <div class="card-tools">
                        <button type="button" class="btn btn-primary btn-sm" onclick="refreshPage()">
                            <i class="fas fa-sync-alt"></i> 刷新
                        </button>
                    </div>
                </div>
                
                <div class="card-body">
                    <!-- 过滤器 -->
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <label for="userFilter">按用户过滤:</label>
                            <input type="number" class="form-control" id="userFilter" placeholder="用户ID" 
                                   value="{{ current_user_id or '' }}">
                        </div>
                        <div class="col-md-4">
                            <label for="groupFilter">按分组过滤:</label>
                            <select class="form-control" id="groupFilter">
                                <option value="">所有分组</option>
                                {% for group in groups %}
                                <option value="{{ group.id }}" 
                                        {% if current_group_id and current_group_id|int == group.id %}selected{% endif %}>
                                    {{ group.name }}
                                </option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label>&nbsp;</label><br>
                            <button type="button" class="btn btn-info" onclick="applyFilters()">
                                <i class="fas fa-filter"></i> 应用过滤
                            </button>
                            <button type="button" class="btn btn-secondary" onclick="clearFilters()">
                                <i class="fas fa-times"></i> 清除
                            </button>
                        </div>
                    </div>
                    
                    <!-- 订阅列表 -->
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>用户</th>
                                    <th>订单ID</th>
                                    <th>分组</th>
                                    <th>状态</th>
                                    <th>创建时间</th>
                                    <th>过期时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for subscription in subscriptions.items %}
                                <tr id="subscription-{{ subscription.id }}">
                                    <td>{{ subscription.id }}</td>
                                    <td>
                                        {% if subscription.order.user %}
                                            {{ subscription.order.user.username }}
                                            <small class="text-muted">(ID: {{ subscription.order.user.id }})</small>
                                        {% else %}
                                            <span class="text-muted">无关联用户</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <code>{{ subscription.order.order_id }}</code>
                                    </td>
                                    <td>
                                        {% if subscription.group %}
                                            <span class="badge bg-info">{{ subscription.group.name }}</span>
                                        {% else %}
                                            <span class="text-muted">未分组</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if subscription.is_active %}
                                            {% if subscription.is_expired %}
                                                <span class="badge bg-warning">已过期</span>
                                            {% else %}
                                                <span class="badge bg-success">活跃</span>
                                            {% endif %}
                                        {% else %}
                                            <span class="badge bg-danger">已停用</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ subscription.created_at.strftime('%Y-%m-%d %H:%M') }}</td>
                                    <td>
                                        {% if subscription.expires_at %}
                                            {{ subscription.expires_at.strftime('%Y-%m-%d %H:%M') }}
                                        {% else %}
                                            <span class="text-muted">永久</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if subscription.is_active %}
                                        <button type="button" class="btn btn-danger btn-sm"
                                                onclick="deleteSubscription({{ subscription.id }}, '{{ subscription.order.order_id }}')">
                                            <i class="fas fa-trash"></i> 软删除
                                        </button>
                                        {% else %}
                                        <div class="d-flex flex-column gap-1">
                                            <span class="text-muted small">已软删除</span>
                                            <button type="button" class="btn btn-outline-danger btn-sm"
                                                    onclick="hardDeleteSubscription({{ subscription.id }}, '{{ subscription.order.order_id }}')">
                                                <i class="fas fa-trash-alt"></i> 彻底删除
                                            </button>
                                        </div>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% else %}
                                <tr>
                                    <td colspan="8" class="text-center text-muted">暂无订阅数据</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- 分页 -->
                    {% if subscriptions.pages > 1 %}
                    <nav aria-label="订阅分页">
                        <ul class="pagination justify-content-center">
                            {% if subscriptions.has_prev %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('admin.subscriptions', page=subscriptions.prev_num, user_id=current_user_id, group_id=current_group_id) }}">上一页</a>
                            </li>
                            {% endif %}
                            
                            {% for page_num in subscriptions.iter_pages() %}
                                {% if page_num %}
                                    {% if page_num != subscriptions.page %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('admin.subscriptions', page=page_num, user_id=current_user_id, group_id=current_group_id) }}">{{ page_num }}</a>
                                    </li>
                                    {% else %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ page_num }}</span>
                                    </li>
                                    {% endif %}
                                {% else %}
                                <li class="page-item disabled">
                                    <span class="page-link">…</span>
                                </li>
                                {% endif %}
                            {% endfor %}
                            
                            {% if subscriptions.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('admin.subscriptions', page=subscriptions.next_num, user_id=current_user_id, group_id=current_group_id) }}">下一页</a>
                            </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 删除确认模态框 -->
<div class="modal fade" id="deleteModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">确认删除订阅</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>确定要删除订阅吗？</p>
                <p><strong>订单ID:</strong> <span id="deleteOrderId"></span></p>

                <div class="mb-3">
                    <label class="form-label">删除类型：</label>
                    <div class="form-check">
                        <input class="form-check-input" type="radio" name="deleteType" id="softDelete" value="soft" checked>
                        <label class="form-check-label" for="softDelete">
                            <strong>软删除</strong> - 停用订阅但保留数据库记录
                        </label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="radio" name="deleteType" id="hardDelete" value="hard">
                        <label class="form-check-label" for="hardDelete">
                            <strong>硬删除</strong> - 完全从数据库中删除（不可恢复）
                        </label>
                    </div>
                </div>

                <p class="text-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    此操作将从X-UI面板删除客户端配置，无法撤销！
                </p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-danger" id="confirmDelete">确认删除</button>
            </div>
        </div>
    </div>
</div>

<script>
let currentSubscriptionId = null;

function refreshPage() {
    window.location.reload();
}

function applyFilters() {
    const userId = document.getElementById('userFilter').value;
    const groupId = document.getElementById('groupFilter').value;
    
    const params = new URLSearchParams();
    if (userId) params.append('user_id', userId);
    if (groupId) params.append('group_id', groupId);
    
    window.location.href = '{{ url_for("admin.subscriptions") }}?' + params.toString();
}

function clearFilters() {
    window.location.href = '{{ url_for("admin.subscriptions") }}';
}

function deleteSubscription(subscriptionId, orderId) {
    console.log('deleteSubscription 被调用:', subscriptionId, orderId);
    currentSubscriptionId = subscriptionId;
    currentHardDelete = false; // 软删除
    document.getElementById('deleteOrderId').textContent = orderId;

    // Bootstrap 5 语法
    try {
        const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
        deleteModal.show();
        console.log('模态框已显示');
    } catch (error) {
        console.error('显示模态框时发生错误:', error);
        // 如果Bootstrap 5不可用，尝试Bootstrap 4语法
        try {
            $('#deleteModal').modal('show');
            console.log('使用Bootstrap 4语法显示模态框');
        } catch (error2) {
            console.error('Bootstrap 4语法也失败:', error2);
            alert('确定要软删除订阅吗？订单ID: ' + orderId);
            // 直接调用删除函数
            if (confirm('确定要软删除订阅吗？')) {
                confirmDeleteAction();
            }
        }
    }
}

function hardDeleteSubscription(subscriptionId, orderId) {
    console.log('hardDeleteSubscription 被调用:', subscriptionId, orderId);

    // 直接确认硬删除，不使用模态框
    if (confirm(`确定要彻底删除订阅吗？\n\n订单ID: ${orderId}\n\n⚠️ 警告：此操作将从数据库中完全删除订阅及其所有相关数据，无法恢复！`)) {
        currentSubscriptionId = subscriptionId;
        currentHardDelete = true; // 硬删除
        confirmDeleteAction();
    }
}

function confirmDeleteAction() {
    if (!currentSubscriptionId) {
        console.error('没有选择要删除的订阅ID');
        return;
    }

    console.log('开始删除订阅:', currentSubscriptionId);

    // 禁用删除按钮，防止重复点击
    const deleteBtn = document.getElementById('confirmDelete');
    if (deleteBtn) {
        deleteBtn.disabled = true;
        deleteBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 删除中...';
    }

    // 获取删除类型
    let hardDelete = false;
    if (typeof currentHardDelete !== 'undefined') {
        hardDelete = currentHardDelete;
    } else {
        const deleteTypeElement = document.querySelector('input[name="deleteType"]:checked');
        if (deleteTypeElement) {
            hardDelete = deleteTypeElement.value === 'hard';
        }
    }

    const deleteUrl = `/admin/api/subscriptions/${currentSubscriptionId}/delete?hard_delete=${hardDelete}`;
    console.log('删除请求URL:', deleteUrl);
    console.log('删除类型:', deleteType, '硬删除:', hardDelete);

    fetch(deleteUrl, {
        method: 'DELETE',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => {
        console.log('删除请求响应状态:', response.status);
        console.log('删除请求响应头:', response.headers);
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        return response.json();
    })
    .then(data => {
        console.log('删除请求响应数据:', data);

        // 恢复删除按钮
        if (deleteBtn) {
            deleteBtn.disabled = false;
            deleteBtn.innerHTML = '确认删除';
        }

        if (data.success) {
            // 隐藏模态框
            try {
                const deleteModal = bootstrap.Modal.getInstance(document.getElementById('deleteModal'));
                if (deleteModal) {
                    deleteModal.hide();
                } else {
                    // 尝试Bootstrap 4语法
                    $('#deleteModal').modal('hide');
                }
            } catch (error) {
                console.error('隐藏模态框失败:', error);
            }

            // 更新表格行
            const row = document.getElementById(`subscription-${currentSubscriptionId}`);
            if (row) {
                if (hardDelete) {
                    // 硬删除：移除整行
                    row.remove();
                } else {
                    // 软删除：更新状态
                    const statusCell = row.cells[4];
                    statusCell.innerHTML = '<span class="badge bg-danger">已停用</span>';

                    const actionCell = row.cells[7];
                    actionCell.innerHTML = `
                        <div class="d-flex flex-column gap-1">
                            <span class="text-muted small">已软删除</span>
                            <button type="button" class="btn btn-outline-danger btn-sm"
                                    onclick="hardDeleteSubscription(${currentSubscriptionId}, '${data.order_id || 'N/A'}')">
                                <i class="fas fa-trash-alt"></i> 彻底删除
                            </button>
                        </div>
                    `;
                }
            }

            // 显示成功消息
            showAlert('success', data.message);

            // 如果有详细信息，也显示在控制台
            if (data.details) {
                console.log('删除详情:', data.details);
            }
        } else {
            showAlert('danger', data.message || '删除订阅失败');
        }
    })
    .catch(error => {
        console.error('删除订阅失败:', error);

        // 恢复删除按钮
        if (deleteBtn) {
            deleteBtn.disabled = false;
            deleteBtn.innerHTML = '确认删除';
        }

        showAlert('danger', `删除订阅失败: ${error.message}`);
    });
}

// 确保DOM加载完成后再绑定事件
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM已加载，绑定删除确认事件');
    const confirmDeleteBtn = document.getElementById('confirmDelete');
    if (confirmDeleteBtn) {
        // 移除可能存在的旧事件监听器
        confirmDeleteBtn.removeEventListener('click', confirmDeleteAction);
        // 添加新的事件监听器
        confirmDeleteBtn.addEventListener('click', confirmDeleteAction);
        console.log('删除确认按钮事件已绑定');
    } else {
        console.error('未找到删除确认按钮');
    }

    // 测试按钮是否可点击
    if (confirmDeleteBtn) {
        console.log('确认删除按钮状态:', {
            disabled: confirmDeleteBtn.disabled,
            innerHTML: confirmDeleteBtn.innerHTML,
            onclick: confirmDeleteBtn.onclick
        });
    }
});

function showAlert(type, message) {
    const alertHtml = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    `;
    
    // 在页面顶部显示警告
    const container = document.querySelector('.container-fluid');
    container.insertAdjacentHTML('afterbegin', alertHtml);
    
    // 3秒后自动隐藏
    setTimeout(() => {
        const alert = container.querySelector('.alert');
        if (alert) {
            alert.remove();
        }
    }, 3000);
}
</script>
{% endblock %}
