"""
X-UI面板相关模型
"""
from datetime import datetime
from . import db
from .enums import PanelStatus, GroupRole


class XUIPanel(db.Model):
    """X-UI面板模型"""
    __tablename__ = 'xui_panels'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    base_url = db.Column(db.String(255), nullable=False)
    path_prefix = db.Column(db.String(50), nullable=False, default='/')
    username = db.Column(db.String(100), nullable=False)
    password = db.Column(db.String(255), nullable=False)

    # 面板配置
    region = db.Column(db.String(50), nullable=False, default='default')
    max_clients = db.Column(db.Integer, nullable=False, default=1000)
    priority = db.Column(db.Integer, nullable=False, default=1)
    status = db.Column(db.Enum(PanelStatus), nullable=False, default=PanelStatus.ACTIVE)

    # 统计信息
    current_clients = db.Column(db.Integer, nullable=False, default=0)
    total_traffic_gb = db.Column(db.Float, nullable=False, default=0)

    # 健康检查
    is_online = db.Column(db.Boolean, nullable=False, default=False)
    last_check = db.Column(db.DateTime, nullable=True)
    response_time = db.Column(db.Float, nullable=False, default=0)
    error_count = db.Column(db.Integer, nullable=False, default=0)
    last_error = db.Column(db.Text, nullable=True)

    # 时间字段
    created_at = db.Column(db.DateTime, nullable=False, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, nullable=False, default=datetime.utcnow, onupdate=datetime.utcnow)

    # 关联关系
    group_memberships = db.relationship('XUIPanelGroupMembership', backref='panel', lazy=True, cascade='all, delete-orphan')

    def __repr__(self):
        return f'<XUIPanel {self.name}>'

    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'name': self.name,
            'base_url': self.base_url,
            'path_prefix': self.path_prefix,
            'region': self.region,
            'max_clients': self.max_clients,
            'priority': self.priority,
            'status': self.status.value if self.status else None,
            'current_clients': self.current_clients,
            'total_traffic_gb': self.total_traffic_gb,
            'is_online': self.is_online,
            'last_check': self.last_check.isoformat() if self.last_check else None,
            'response_time': self.response_time,
            'error_count': self.error_count,
            'last_error': self.last_error,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'groups': [membership.group.to_dict() for membership in self.group_memberships if membership.group]
        }

    @property
    def groups(self):
        """获取面板所属的所有分组"""
        return [membership.group for membership in self.group_memberships if membership.group]

    @property
    def active_groups(self):
        """获取面板所属的活跃分组"""
        return [membership.group for membership in self.group_memberships
                if membership.group and membership.group.is_active]

    def get_group_membership(self, group_id):
        """获取在指定分组中的成员关系"""
        for membership in self.group_memberships:
            if membership.group_id == group_id:
                return membership
        return None

    def is_in_group(self, group_id):
        """检查是否在指定分组中"""
        return self.get_group_membership(group_id) is not None


class XUIPanelGroup(db.Model):
    """X-UI面板分组模型"""
    __tablename__ = 'xui_panel_groups'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False, unique=True)
    description = db.Column(db.Text, nullable=True)
    color = db.Column(db.String(7), nullable=False, default='#007bff')  # 十六进制颜色代码
    priority = db.Column(db.Integer, nullable=False, default=1)  # 优先级，数字越小优先级越高
    is_active = db.Column(db.Boolean, nullable=False, default=True)

    # 时间字段
    created_at = db.Column(db.DateTime, nullable=False, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, nullable=False, default=datetime.utcnow, onupdate=datetime.utcnow)

    # 关联关系
    memberships = db.relationship('XUIPanelGroupMembership', backref='group', lazy=True, cascade='all, delete-orphan')

    def __repr__(self):
        return f'<XUIPanelGroup {self.name}>'

    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'color': self.color,
            'priority': self.priority,
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'panel_count': len(self.memberships)
        }

    @property
    def panels(self):
        """获取分组中的所有面板"""
        return [membership.panel for membership in self.memberships if membership.panel]

    @property
    def active_panels(self):
        """获取分组中的活跃面板"""
        return [membership.panel for membership in self.memberships
                if membership.panel and membership.panel.status == PanelStatus.ACTIVE]


class XUIPanelGroupMembership(db.Model):
    """X-UI面板分组成员关系模型（多对多中间表）"""
    __tablename__ = 'xui_panel_group_memberships'

    id = db.Column(db.Integer, primary_key=True)
    panel_id = db.Column(db.Integer, db.ForeignKey('xui_panels.id'), nullable=False)
    group_id = db.Column(db.Integer, db.ForeignKey('xui_panel_groups.id'), nullable=False)

    # 在分组中的权重（用于负载均衡）
    weight = db.Column(db.Integer, nullable=False, default=1)

    # 在分组中的角色
    role = db.Column(db.Enum(GroupRole), nullable=False, default=GroupRole.PRIMARY)

    # 指定的入站协议ID
    inbound_id = db.Column(db.Integer, nullable=True)  # 可空，用于向后兼容

    # 时间字段
    created_at = db.Column(db.DateTime, nullable=False, default=datetime.utcnow)

    # 唯一约束：一个面板在同一个分组中只能有一个成员关系
    __table_args__ = (db.UniqueConstraint('panel_id', 'group_id', name='unique_panel_group'),)

    def __repr__(self):
        return f'<XUIPanelGroupMembership panel_id={self.panel_id} group_id={self.group_id}>'

    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'panel_id': self.panel_id,
            'group_id': self.group_id,
            'weight': self.weight,
            'role': self.role.value if self.role else None,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }
