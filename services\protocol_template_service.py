"""
协议模板服务 - 处理协议模板的解析和配置生成
"""
import json
import base64
import logging
import re
from typing import Dict, Optional, List, Tuple
from models import db, ProtocolTemplate, NodeType

logger = logging.getLogger(__name__)


class ProtocolTemplateService:
    """协议模板服务类"""

    def __init__(self):
        pass

    def create_default_templates(self) -> bool:
        """创建默认协议模板"""
        try:
            # 检查是否已存在默认模板
            existing_count = ProtocolTemplate.query.filter_by(is_default=True).count()
            if existing_count > 0:
                logger.info(f"已存在 {existing_count} 个默认模板，跳过创建")
                return True

            default_templates = ProtocolTemplate.get_default_templates()
            created_count = 0

            for template_data in default_templates:
                # 检查是否已存在同名模板
                existing = ProtocolTemplate.query.filter_by(name=template_data['name']).first()
                if existing:
                    logger.info(f"模板 {template_data['name']} 已存在，跳过创建")
                    continue

                template = ProtocolTemplate(
                    name=template_data['name'],
                    description=template_data['description'],
                    protocol_type=template_data['protocol_type'],
                    template_content=template_data['template_content'],
                    is_default=template_data['is_default'],
                    is_active=True
                )

                db.session.add(template)
                created_count += 1
                logger.info(f"创建默认模板: {template_data['name']}")

            if created_count > 0:
                db.session.commit()
                logger.info(f"成功创建 {created_count} 个默认协议模板")

            return True

        except Exception as e:
            logger.error(f"创建默认协议模板失败: {e}")
            db.session.rollback()
            return False

    def parse_template(self, template_content: str, variables: Dict) -> str:
        """解析模板并替换占位符"""
        try:
            # 处理特殊的VMess配置
            if '{vmess_config_base64}' in template_content:
                vmess_config = self._generate_vmess_config(variables)
                variables['vmess_config_base64'] = vmess_config

            # 替换所有占位符
            result = template_content
            for key, value in variables.items():
                placeholder = f"{{{key}}}"
                result = result.replace(placeholder, str(value))

            return result

        except Exception as e:
            logger.error(f"解析模板失败: {e}")
            return template_content

    def _generate_vmess_config(self, variables: Dict) -> str:
        """生成VMess配置的Base64编码"""
        try:
            vmess_config = {
                "v": "2",
                "ps": variables.get('client_email', ''),
                "add": variables.get('server_address', ''),
                "port": str(variables.get('server_port', 443)),
                "id": variables.get('client_id', ''),
                "aid": "0",
                "scy": "auto",
                "net": variables.get('network', 'tcp'),
                "type": "none",
                "host": variables.get('ws_host', ''),
                "path": variables.get('ws_path', '/'),
                "tls": "tls" if variables.get('security') == 'tls' else "",
                "sni": variables.get('server_name', ''),
                "alpn": ""
            }

            # 转换为JSON字符串并Base64编码
            config_json = json.dumps(vmess_config, separators=(',', ':'))
            config_base64 = base64.b64encode(config_json.encode('utf-8')).decode('utf-8')
            return config_base64

        except Exception as e:
            logger.error(f"生成VMess配置失败: {e}")
            return ""

    def extract_variables_from_inbound(self, inbound_config: Dict, panel_config: Dict, client_data: Dict) -> Dict:
        """从入站配置中提取变量"""
        try:
            variables = {
                'client_id': client_data.get('id', ''),
                'client_email': client_data.get('email', ''),
                'server_address': panel_config.get('server_address', ''),
                'server_port': inbound_config.get('port', 443),
                'protocol': inbound_config.get('protocol', 'vless'),
            }

            # 解析streamSettings
            stream_settings_str = inbound_config.get('streamSettings', '{}')
            try:
                if isinstance(stream_settings_str, str):
                    stream_settings = json.loads(stream_settings_str)
                else:
                    stream_settings = stream_settings_str
            except:
                stream_settings = {}

            # 添加传输层配置
            variables.update({
                'network': stream_settings.get('network', 'tcp'),
                'security': stream_settings.get('security', 'none'),
            })

            # 添加TLS相关配置
            if variables['security'] == 'tls':
                tls_settings = stream_settings.get('tlsSettings', {})
                variables['server_name'] = tls_settings.get('serverName', variables['server_address'])
                variables['alpn'] = ','.join(tls_settings.get('alpn', []))

            # 添加WebSocket相关配置
            if variables['network'] == 'ws':
                ws_settings = stream_settings.get('wsSettings', {})
                variables['ws_path'] = ws_settings.get('path', '/')
                headers = ws_settings.get('headers', {})
                variables['ws_host'] = headers.get('Host', variables['server_address'])

            # 添加gRPC相关配置
            elif variables['network'] == 'grpc':
                grpc_settings = stream_settings.get('grpcSettings', {})
                variables['grpc_service_name'] = grpc_settings.get('serviceName', '')

            # 添加HTTP/2相关配置
            elif variables['network'] == 'h2':
                http_settings = stream_settings.get('httpSettings', {})
                variables['h2_path'] = http_settings.get('path', '/')
                variables['h2_host'] = ','.join(http_settings.get('host', []))

            return variables

        except Exception as e:
            logger.error(f"提取入站配置变量失败: {e}")
            return {
                'client_id': client_data.get('id', ''),
                'client_email': client_data.get('email', ''),
                'server_address': panel_config.get('server_address', ''),
                'server_port': inbound_config.get('port', 443),
                'protocol': 'vless',
                'network': 'tcp',
                'security': 'none'
            }

    def generate_config(self, template_id: int, inbound_config: Dict, panel_config: Dict, client_data: Dict) -> Optional[str]:
        """使用协议模板生成配置"""
        try:
            # 获取协议模板
            template = ProtocolTemplate.query.get(template_id)
            if not template or not template.is_active:
                logger.error(f"协议模板 {template_id} 不存在或未激活")
                return None

            # 提取变量
            variables = self.extract_variables_from_inbound(inbound_config, panel_config, client_data)

            # 解析模板
            config = self.parse_template(template.template_content, variables)

            logger.info(f"使用模板 {template.name} 生成配置成功")
            return config

        except Exception as e:
            logger.error(f"使用协议模板生成配置失败: {e}")
            return None

    def validate_template(self, template_content: str, protocol_type: NodeType) -> Tuple[bool, List[str]]:
        """验证模板内容"""
        try:
            # 获取模板中的占位符
            placeholders = re.findall(r'\{([^}]+)\}', template_content)
            
            # 必需的基础占位符
            required_placeholders = ['client_id', 'server_address', 'server_port', 'client_email']
            
            # 检查缺失的占位符
            missing_placeholders = []
            for placeholder in required_placeholders:
                if placeholder not in placeholders:
                    missing_placeholders.append(placeholder)

            # 协议特定验证
            if protocol_type == NodeType.VMESS and '{vmess_config_base64}' not in placeholders:
                missing_placeholders.append('vmess_config_base64')

            return len(missing_placeholders) == 0, missing_placeholders

        except Exception as e:
            logger.error(f"验证模板失败: {e}")
            return False, [str(e)]

    def get_available_placeholders(self) -> Dict[str, str]:
        """获取可用的占位符及其说明"""
        return {
            'client_id': '客户端UUID',
            'client_email': '客户端邮箱/名称',
            'server_address': '服务器地址',
            'server_port': '服务器端口',
            'protocol': '协议类型',
            'network': '传输协议 (tcp/ws/grpc/h2)',
            'security': '安全类型 (none/tls)',
            'server_name': 'TLS服务器名称',
            'alpn': 'ALPN协议',
            'ws_path': 'WebSocket路径',
            'ws_host': 'WebSocket主机头',
            'grpc_service_name': 'gRPC服务名',
            'h2_path': 'HTTP/2路径',
            'h2_host': 'HTTP/2主机',
            'vmess_config_base64': 'VMess配置Base64编码'
        }
