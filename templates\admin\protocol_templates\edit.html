{% extends "base.html" %}

{% block title %}编辑协议模板 - 管理后台{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="row">
        <div class="col-12">
            <h1>
                <i class="bi bi-pencil"></i> 编辑协议模板：{{ template.name }}
            </h1>
        </div>
    </div>
    
    <div class="row justify-content-center">
        <div class="col-md-10">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">协议模板配置</h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ url_for('protocol_template.edit_template', template_id=template.id) }}">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="name" class="form-label">模板名称 *</label>
                                    <input type="text" class="form-control" id="name" name="name" required 
                                           value="{{ template.name }}" placeholder="例如：VLESS-TCP-TLS-Custom">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="protocol_type" class="form-label">协议类型 *</label>
                                    <select class="form-select" id="protocol_type" name="protocol_type" required>
                                        <option value="">选择协议类型...</option>
                                        {% for node_type in node_types %}
                                        <option value="{{ node_type.value }}" {{ 'selected' if template.protocol_type == node_type else '' }}>
                                            {{ node_type.value.upper() }}
                                        </option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="description" class="form-label">模板描述</label>
                            <textarea class="form-control" id="description" name="description" rows="2" 
                                      placeholder="描述这个协议模板的用途和特点">{{ template.description or '' }}</textarea>
                        </div>
                        
                        <div class="mb-3">
                            <label for="template_content" class="form-label">模板内容 *</label>
                            <textarea class="form-control" id="template_content" name="template_content" rows="8" required 
                                      placeholder="输入协议配置模板，使用 {占位符} 表示动态内容">{{ template.template_content }}</textarea>
                            <div class="form-text">
                                使用占位符来表示动态内容，例如：{client_id}, {server_address}, {server_port} 等
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="is_active" name="is_active" 
                                       {{ 'checked' if template.is_active else '' }}>
                                <label class="form-check-label" for="is_active">
                                    启用此模板
                                </label>
                            </div>
                            {% if template.is_default %}
                            <div class="form-text text-info">
                                <i class="bi bi-info-circle"></i> 这是系统默认模板，无法禁用
                            </div>
                            {% endif %}
                        </div>
                        
                        <div class="d-flex justify-content-between">
                            <a href="{{ url_for('protocol_template.list_templates') }}" class="btn btn-outline-secondary">
                                <i class="bi bi-arrow-left"></i> 返回列表
                            </a>
                            <div>
                                <button type="button" class="btn btn-outline-info me-2" onclick="validateTemplate()">
                                    <i class="bi bi-check-circle"></i> 验证模板
                                </button>
                                <button type="button" class="btn btn-outline-success me-2" onclick="previewTemplate()">
                                    <i class="bi bi-eye"></i> 预览模板
                                </button>
                                <button type="submit" class="btn btn-primary">
                                    <i class="bi bi-save"></i> 保存更改
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 占位符参考 -->
    <div class="row mt-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0"><i class="bi bi-info-circle"></i> 可用占位符</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        {% for placeholder, description in placeholders.items() %}
                        <div class="col-12 mb-2">
                            <code>{{"{"}}{{ placeholder }}{{"}"}} </code>
                            <small class="text-muted">- {{ description }}</small>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0"><i class="bi bi-lightbulb"></i> 模板示例</h6>
                </div>
                <div class="card-body">
                    <h6>VLESS TCP TLS:</h6>
                    <pre class="bg-light p-2 rounded small">vless://{client_id}@{server_address}:{server_port}?security=tls&encryption=none&type=tcp&sni={server_name}#{client_email}</pre>
                    
                    <h6 class="mt-3">Trojan TCP TLS:</h6>
                    <pre class="bg-light p-2 rounded small">trojan://{client_id}@{server_address}:{server_port}?security=tls&type=tcp&sni={server_name}#{client_email}</pre>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 预览模态框 -->
<div class="modal fade" id="previewModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">协议模板预览</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <pre id="previewContent" class="bg-light p-3 rounded"></pre>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>

<script>
function validateTemplate() {
    const templateContent = document.getElementById('template_content').value;
    const protocolType = document.getElementById('protocol_type').value;
    
    if (!templateContent || !protocolType) {
        alert('请先填写模板内容和协议类型');
        return;
    }
    
    fetch('/admin/api/protocol-templates/validate', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            template_content: templateContent,
            protocol_type: protocolType
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            if (data.is_valid) {
                alert('✓ 模板验证通过！');
            } else {
                alert('✗ 模板验证失败，缺少必需占位符: ' + data.missing_placeholders.join(', '));
            }
        } else {
            alert('验证失败: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('验证失败');
    });
}

function previewTemplate() {
    fetch(`/admin/api/protocol-templates/{{ template.id }}/preview`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            document.getElementById('previewContent').textContent = data.preview;
            new bootstrap.Modal(document.getElementById('previewModal')).show();
        } else {
            alert('预览失败: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('预览失败');
    });
}

// 协议类型变化时提供相应的模板示例
document.getElementById('protocol_type').addEventListener('change', function() {
    const protocolType = this.value;
    const templateContent = document.getElementById('template_content');
    
    // 只在模板内容为空时提供示例
    if (protocolType && !templateContent.value.trim()) {
        let example = '';
        switch(protocolType) {
            case 'vless':
                example = 'vless://{client_id}@{server_address}:{server_port}?security=tls&encryption=none&type=tcp&sni={server_name}#{client_email}';
                break;
            case 'vmess':
                example = 'vmess://{vmess_config_base64}';
                break;
            case 'trojan':
                example = 'trojan://{client_id}@{server_address}:{server_port}?security=tls&type=tcp&sni={server_name}#{client_email}';
                break;
        }
        if (example) {
            templateContent.value = example;
        }
    }
});

// 默认模板禁用状态处理
{% if template.is_default %}
document.getElementById('is_active').disabled = true;
document.getElementById('is_active').checked = true;
{% endif %}
</script>

{% endblock %}
